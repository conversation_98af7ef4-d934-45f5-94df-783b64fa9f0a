@import 'tailwindcss';

/* Space Theme Animations - Optimized for performance */
@keyframes spin-slow {
  from {
    transform: rotate3d(0, 0, 1, 0deg);
  }
  to {
    transform: rotate3d(0, 0, 1, 360deg);
  }
}

.animate-spin-slow {
  animation: spin-slow 8s linear infinite;
  will-change: transform;
}

/* Company Logos Slideshow Animation - Optimized */
@keyframes scroll {
  0% {
    transform: translate3d(0, 0, 0);
  }
  100% {
    transform: translate3d(-50%, 0, 0);
  }
}

.animate-scroll {
  animation: scroll 20s linear infinite;
  will-change: transform;
}

/* Animated Stars Background - Optimized for performance */
.stars {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: transparent
    url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="20" cy="20" r="0.5" fill="white" opacity="0.8"/><circle cx="80" cy="40" r="0.3" fill="white" opacity="0.6"/><circle cx="40" cy="60" r="0.4" fill="white" opacity="0.7"/><circle cx="90" cy="80" r="0.2" fill="white" opacity="0.5"/><circle cx="10" cy="90" r="0.3" fill="white" opacity="0.6"/><circle cx="70" cy="10" r="0.4" fill="white" opacity="0.8"/><circle cx="30" cy="30" r="0.2" fill="white" opacity="0.4"/><circle cx="60" cy="70" r="0.3" fill="white" opacity="0.7"/><circle cx="85" cy="15" r="0.2" fill="white" opacity="0.5"/><circle cx="15" cy="50" r="0.4" fill="white" opacity="0.6"/></svg>')
    repeat;
  background-size: 200px 200px;
  animation: move-stars 20s linear infinite;
  will-change: transform;
}

.twinkling {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: transparent
    url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="25" cy="25" r="0.3" fill="white" opacity="0.9"/><circle cx="75" cy="75" r="0.2" fill="white" opacity="0.8"/><circle cx="50" cy="10" r="0.4" fill="white" opacity="0.7"/><circle cx="10" cy="60" r="0.2" fill="white" opacity="0.6"/><circle cx="90" cy="30" r="0.3" fill="white" opacity="0.8"/></svg>')
    repeat;
  background-size: 300px 300px;
  animation: move-twinkling 30s linear infinite;
  will-change: transform;
}

@keyframes move-stars {
  from {
    transform: translate3d(0, 0, 0);
  }
  to {
    transform: translate3d(0, -200px, 0);
  }
}

@keyframes move-twinkling {
  from {
    transform: translate3d(0, 0, 0);
  }
  to {
    transform: translate3d(0, -300px, 0);
  }
}

/* Glowing effects for space theme */
.glow-purple {
  box-shadow: 0 0 20px rgba(147, 51, 234, 0.5);
}

.glow-blue {
  box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);
}

/* Custom scrollbar for space theme */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #1e293b;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(to bottom, #8b5cf6, #3b82f6);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(to bottom, #7c3aed, #2563eb);
}

:root {
  --background: #ffffff;
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #0a0a0a;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
  /* Performance optimizations */
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  /* Security: Prevent text selection on sensitive elements */
  -webkit-user-select: text;
  -moz-user-select: text;
  -ms-user-select: text;
  user-select: text;
  /* Accessibility */
  scroll-behavior: smooth;
}

/* Performance: GPU acceleration for animations */
.animate-pulse,
.animate-bounce,
.animate-ping,
.animate-spin {
  will-change: transform;
  transform: translateZ(0);
  backface-visibility: hidden;
  perspective: 1000px;
}

/* Optimized transitions */
.smooth-transition {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  will-change: transform, opacity;
}

.fast-transition {
  transition: all 0.15s ease-out;
}

/* Reduce backdrop blur for better performance */
.backdrop-blur-light {
  -webkit-backdrop-filter: blur(4px);
  backdrop-filter: blur(4px);
}

.backdrop-blur-medium {
  -webkit-backdrop-filter: blur(8px);
  backdrop-filter: blur(8px);
}

/* Optimized hover effects */
.hover-scale {
  transition: transform 0.2s ease-out;
}

.hover-scale:hover {
  transform: scale(1.02);
}

/* Grid background for projects section */
.bg-grid-purple {
  background-image:
    linear-gradient(rgba(139, 92, 246, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(139, 92, 246, 0.1) 1px, transparent 1px);
  background-size: 40px 40px;
  animation: pulse 6s ease-in-out infinite;
}

/* Grid background for hero section */
.bg-grid-cyan {
  background-image:
    linear-gradient(rgba(6, 182, 212, 0.1) 1px, transparent 1px),
    linear-gradient(90deg, rgba(6, 182, 212, 0.1) 1px, transparent 1px);
  background-size: 50px 50px;
  animation: pulse 4s ease-in-out infinite;
}

/* Animation delay classes */
.delay-1s {
  animation-delay: 1s;
}

.delay-2s {
  animation-delay: 2s;
}

.delay-0-5s {
  animation-delay: 0.5s;
}

.delay-3s {
  animation-delay: 3s;
}

.delay-4s {
  animation-delay: 4s;
}

.delay-5s {
  animation-delay: 5s;
}

/* Skill progress bar styles */
.skill-progress-bar {
  height: 0.5rem;
  border-radius: 9999px;
  background: linear-gradient(to right, var(--progress-from), var(--progress-to));
  animation: pulse 2s ease-in-out infinite;
  width: var(--skill-width, 0%);
  transition: width 0.8s ease-out;
}

.skill-progress-bar.blue-purple {
  --progress-from: #60a5fa;
  --progress-to: #a855f7;
}

.skill-progress-bar.orange-red {
  --progress-from: #fb923c;
  --progress-to: #ef4444;
}

.skill-progress-bar.cyan-blue {
  --progress-from: #22d3ee;
  --progress-to: #3b82f6;
}

.skill-progress-bar.green-teal {
  --progress-from: #4ade80;
  --progress-to: #14b8a6;
}

.skill-progress-bar.pink-purple {
  --progress-from: #f472b6;
  --progress-to: #a855f7;
}

.skill-progress-bar.blue-cyan {
  --progress-from: #93c5fd;
  --progress-to: #22d3ee;
}

/* Smooth scroll behavior */
html {
  scroll-behavior: smooth;
}

/* Optimize carousel transitions */
.carousel-container {
  will-change: transform;
  transform: translateZ(0);
}

.carousel-slide {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Security: Hide content from screen readers when needed */
.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

/* Performance: Optimize images */
img {
  max-width: 100%;
  height: auto;
  /* Lazy loading hint */
  loading: lazy;
  /* Prevent layout shift */
  aspect-ratio: attr(width) / attr(height);
}

/* Security: Prevent clickjacking */
iframe {
  border: 0;
  sandbox: allow-scripts allow-same-origin;
}

/* Performance: Optimize focus states */
:focus-visible {
  outline: 2px solid #8b5cf6;
  outline-offset: 2px;
}

/* Remove default focus for mouse users */
:focus:not(:focus-visible) {
  outline: none;
}

/* Skill Progress Bars */
.skill-progress-bar {
  height: 100%;
  border-radius: 9999px;
  transition: width 1s ease-in-out;
  width: var(--skill-width, 0%);
  will-change: width;
}

.skill-progress-bar.blue-purple {
  background: linear-gradient(90deg, #3b82f6, #8b5cf6);
}

.skill-progress-bar.orange-red {
  background: linear-gradient(90deg, #f97316, #ef4444);
}

.skill-progress-bar.cyan-blue {
  background: linear-gradient(90deg, #06b6d4, #3b82f6);
}

.skill-progress-bar.green-teal {
  background: linear-gradient(90deg, #10b981, #14b8a6);
}

.skill-progress-bar.pink-purple {
  background: linear-gradient(90deg, #ec4899, #8b5cf6);
}

.skill-progress-bar.blue-cyan {
  background: linear-gradient(90deg, #3b82f6, #06b6d4);
}

/* Accessibility: Focus styles */
.focus\:not-sr-only:focus {
  position: absolute !important;
  width: auto !important;
  height: auto !important;
  padding: 0.5rem 1rem !important;
  margin: 0 !important;
  overflow: visible !important;
  clip: auto !important;
  white-space: normal !important;
}

/* Enhanced focus styles for better accessibility */
button:focus-visible,
a:focus-visible,
input:focus-visible,
textarea:focus-visible,
select:focus-visible {
  outline: 2px solid #8b5cf6;
  outline-offset: 2px;
  border-radius: 0.375rem;
}

/* Skip links */
.sr-only {
  position: absolute !important;
  width: 1px !important;
  height: 1px !important;
  padding: 0 !important;
  margin: -1px !important;
  overflow: hidden !important;
  clip: rect(0, 0, 0, 0) !important;
  white-space: nowrap !important;
  border: 0 !important;
}

/* Performance: Reduce motion for users who prefer it */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }

  .animate-pulse,
  .animate-bounce,
  .animate-ping,
  .animate-spin {
    animation: none !important;
  }

  .skill-progress-bar {
    transition: none !important;
  }
}
