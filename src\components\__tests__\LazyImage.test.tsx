import { render, screen, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import LazyImage from '../LazyImage';

// Mock react-intersection-observer
jest.mock('react-intersection-observer', () => ({
  useInView: jest.fn(),
}));

// Mock Next.js Image component
jest.mock('next/image', () => {
  return function MockImage({ src, alt, onLoad, onError, ...props }: any) {
    return (
      <img
        src={src}
        alt={alt}
        onLoad={onLoad}
        onError={onError}
        data-testid="next-image"
        {...props}
      />
    );
  };
});

const mockUseInView = require('react-intersection-observer').useInView as jest.Mock;

describe('LazyImage', () => {
  const defaultProps = {
    src: '/test-image.jpg',
    alt: 'Test image',
    width: 300,
    height: 200,
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('renders placeholder when not in view', () => {
    mockUseInView.mockReturnValue([null, false]);

    render(<LazyImage {...defaultProps} />);

    expect(screen.getByTestId('lazy-image-placeholder')).toBeInTheDocument();
    expect(screen.queryByTestId('next-image')).not.toBeInTheDocument();
  });

  it('renders image when in view', () => {
    mockUseInView.mockReturnValue([null, true]);

    render(<LazyImage {...defaultProps} />);

    expect(screen.getByTestId('next-image')).toBeInTheDocument();
    expect(screen.getByAltText('Test image')).toBeInTheDocument();
  });

  it('renders image immediately when priority is true', () => {
    mockUseInView.mockReturnValue([null, false]);

    render(<LazyImage {...defaultProps} priority={true} />);

    expect(screen.getByTestId('next-image')).toBeInTheDocument();
  });

  it('calls onLoad when image loads successfully', async () => {
    const onLoad = jest.fn();
    mockUseInView.mockReturnValue([null, true]);

    render(<LazyImage {...defaultProps} onLoad={onLoad} />);

    const image = screen.getByTestId('next-image');
    image.dispatchEvent(new Event('load'));

    await waitFor(() => {
      expect(onLoad).toHaveBeenCalledTimes(1);
    });
  });

  it('calls onError when image fails to load', async () => {
    const onError = jest.fn();
    mockUseInView.mockReturnValue([null, true]);

    render(<LazyImage {...defaultProps} onError={onError} />);

    const image = screen.getByTestId('next-image');
    image.dispatchEvent(new Event('error'));

    await waitFor(() => {
      expect(onError).toHaveBeenCalledTimes(1);
    });
  });

  it('applies custom className', () => {
    mockUseInView.mockReturnValue([null, true]);

    render(<LazyImage {...defaultProps} className="custom-class" />);

    const image = screen.getByTestId('next-image');
    expect(image).toHaveClass('custom-class');
  });

  it('shows custom placeholder when provided', () => {
    mockUseInView.mockReturnValue([null, false]);

    render(<LazyImage {...defaultProps} placeholder="/placeholder.jpg" />);

    const placeholder = screen.getByTestId('lazy-image-placeholder');
    expect(placeholder).toHaveStyle('background-image: url(/placeholder.jpg)');
  });

  it('handles missing width and height gracefully', () => {
    mockUseInView.mockReturnValue([null, true]);

    render(<LazyImage src="/test.jpg" alt="Test" />);

    expect(screen.getByTestId('next-image')).toBeInTheDocument();
  });
});
